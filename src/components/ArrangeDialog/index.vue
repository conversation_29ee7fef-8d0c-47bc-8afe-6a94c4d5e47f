<template>
  <el-dialog
    v-model="visible"
    title="Window Arrangement"
    width="95%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    append-to-body
    class="el-dialog--beautify arrange-dialog"
    @closed="onClosed"
  >
    <div class="arrange-container">
      <!-- Control Panel -->
      <div class="control-panel">
        <div class="control-section">
          <el-button-group>
            <el-button
              type="primary"
              :icon="Plus"
              :disabled="availableDevices.length === 0"
              @click="addDevice"
            >
              Add Device
            </el-button>
            <el-button
              type="default"
              :icon="Refresh"
              @click="resetLayout"
            >
              Reset Layout
            </el-button>
          </el-button-group>
        </div>

        <div class="control-section">
          <el-select
            v-model="selectedScope"
            placeholder="Select Configuration Scope"
            style="width: 200px"
            @change="onScopeChange"
          >
            <el-option
              label="Global (Default for all devices)"
              value="global"
            />
            <el-option
              v-for="device in allDevices"
              :key="device.id"
              :label="device.name || device.id"
              :value="device.id"
            />
          </el-select>
        </div>

        <div class="control-section">
          <el-button
            type="danger"
            :icon="Delete"
            :disabled="selectedScope === 'global'"
            @click="deleteCurrentLayout"
          >
            Delete Layout
          </el-button>
        </div>
      </div>

      <!-- Configuration Info -->
      <div class="config-info">
        <el-alert
          v-if="selectedScope === 'global'"
          title="Global Configuration Mode"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            Configure default window layout for all devices. This will be used as fallback when devices don't have individual configurations.
          </template>
        </el-alert>
        <el-alert
          v-else
          :title="`Device-Specific Configuration: ${allDevices.find(d => d.id === selectedScope)?.name || selectedScope}`"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            Configure window layout specifically for this device. This will override the global configuration for this device only.
          </template>
        </el-alert>
      </div>

      <!-- Arrangement Area -->
      <div ref="arrangementAreaRef" class="arrangement-area">
        <div
          ref="screenContainerRef"
          class="screen-container"
          :style="screenContainerStyle"
        >
          <VueDraggableResizable
            v-for="item in arrangedDevices"
            :key="item.id"
            :x="item.x"
            :y="item.y"
            :w="item.width"
            :h="item.height"
            :min-width="50"
            :min-height="50"
            :parent="true"
            :grid="[5, 5]"
            class="device-window"
            @dragging="(x, y) => onDeviceDragging(item.id, { x, y })"
            @resizing="(x, y, w, h) => onDeviceResizing(item.id, { x, y, width: w, height: h })"
            @drag-stop="(x, y) => onDeviceDragStop(item.id, { x, y })"
            @resize-stop="(x, y, w, h) => onDeviceResizeStop(item.id, { x, y, width: w, height: h })"
          >
            <div class="device-content">
              <div class="device-header">
                <span class="device-name">{{ item.name || item.id }}</span>
                <el-button
                  type="danger"
                  :icon="Close"
                  size="small"
                  circle
                  class="remove-btn"
                  @click="removeDevice(item.id)"
                />
              </div>
              <div class="device-body">
                <div class="device-info">
                  <p>Size: {{ Math.round(item.realWidth) }}x{{ Math.round(item.realHeight) }}</p>
                  <p>Position: {{ Math.round(item.realX) }}, {{ Math.round(item.realY) }}</p>
                </div>
              </div>
            </div>
          </VueDraggableResizable>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="close">
        Cancel
      </el-button>
      <el-button type="primary" @click="saveLayout">
        Save Layout
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, nextTick, ref } from 'vue'
import { Close, Delete, Plus, Refresh } from '@element-plus/icons-vue'
import VueDraggableResizable from './vue-draggable-resizable/src/components/vue-draggable-resizable.vue'
import './vue-draggable-resizable/src/components/vue-draggable-resizable.css'

const visible = ref(false)
const arrangementAreaRef = ref(null)
const screenContainerRef = ref(null)
const selectedScope = ref('global')

// Screen and scaling properties
const screenInfo = ref({
  width: 1920,
  height: 1080,
  scaleFactor: 1,
})

const containerDimensions = ref({
  width: 800,
  height: 600,
})

// Device management
const allDevices = ref([])
const arrangedDevices = ref([])
const availableDevices = computed(() => {
  const scope = selectedScope.value
  const arrangedIds = arrangedDevices.value.map(d => d.id)

  if (scope === 'global') {
    // 全局模式：显示所有未安排的设备
    return allDevices.value.filter(d => !arrangedIds.includes(d.id))
  }
  else {
    // 设备特定模式：只显示当前选中的设备（如果未安排）
    const targetDevice = allDevices.value.find(d => d.id === scope)
    if (targetDevice && !arrangedIds.includes(targetDevice.id)) {
      return [targetDevice]
    }
    return []
  }
})

// Computed styles
const screenContainerStyle = computed(() => {
  const aspectRatio = screenInfo.value.width / screenInfo.value.height
  const containerWidth = containerDimensions.value.width
  const containerHeight = containerDimensions.value.height

  let width, height

  if (containerWidth / containerHeight > aspectRatio) {
    // Container is wider than screen aspect ratio
    height = containerHeight
    width = height * aspectRatio
  }
  else {
    // Container is taller than screen aspect ratio
    width = containerWidth
    height = width / aspectRatio
  }

  return {
    width: `${width}px`,
    height: `${height}px`,
    backgroundColor: '#f5f5f5',
    border: '2px solid #ddd',
    position: 'relative',
    margin: '0 auto',
  }
})

// Scaling functions
const scaleToContainer = (realValue, isWidth = true) => {
  const screenDimension = isWidth ? screenInfo.value.width : screenInfo.value.height
  const containerDimension = isWidth
    ? Number.parseInt(screenContainerStyle.value.width)
    : Number.parseInt(screenContainerStyle.value.height)

  return (realValue / screenDimension) * containerDimension
}

const scaleToReal = (containerValue, isWidth = true) => {
  const screenDimension = isWidth ? screenInfo.value.width : screenInfo.value.height
  const containerDimension = isWidth
    ? Number.parseInt(screenContainerStyle.value.width)
    : Number.parseInt(screenContainerStyle.value.height)

  return (containerValue / containerDimension) * screenDimension
}

// Device management functions
const loadDevices = () => {
  const devices = window.appStore.get('device') || {}
  allDevices.value = Object.values(devices).map(device => ({
    ...device,
    name: device.name || device.model?.split(':')[1] || device.id,
  }))
}

const loadLayout = (scope = selectedScope.value) => {
  arrangedDevices.value = []

  if (scope === 'global') {
    // 全局配置模式：显示全局配置的布局
    const globalConfig = window.appStore.get('scrcpy.global') || {}

    if (globalConfig['--window-width'] && globalConfig['--window-height']) {
      // 如果全局配置存在窗口设置，为所有设备应用相同的布局
      allDevices.value.forEach((device, index) => {
        const realWidth = Number.parseInt(globalConfig['--window-width']) || 300
        const realHeight = Number.parseInt(globalConfig['--window-height']) || 600
        const realX = (Number.parseInt(globalConfig['--window-x']) || 0) + (index * 50)
        const realY = (Number.parseInt(globalConfig['--window-y']) || 0) + (index * 50)

        arrangedDevices.value.push({
          ...device,
          x: scaleToContainer(realX, true),
          y: scaleToContainer(realY, false),
          width: scaleToContainer(realWidth, true),
          height: scaleToContainer(realHeight, false),
          realX,
          realY,
          realWidth,
          realHeight,
        })
      })
    }
  }
  else {
    // 设备特定配置模式：只显示该设备的配置
    const device = allDevices.value.find(d => d.id === scope)
    if (device) {
      const deviceConfig = window.appStore.get(`scrcpy.${scope}`) || {}
      const globalConfig = window.appStore.get('scrcpy.global') || {}

      // 设备配置优先，全局配置作为回退
      const config = { ...globalConfig, ...deviceConfig }

      if (config['--window-width'] && config['--window-height']) {
        const realWidth = Number.parseInt(config['--window-width']) || 300
        const realHeight = Number.parseInt(config['--window-height']) || 600
        const realX = Number.parseInt(config['--window-x']) || 0
        const realY = Number.parseInt(config['--window-y']) || 0

        arrangedDevices.value.push({
          ...device,
          x: scaleToContainer(realX, true),
          y: scaleToContainer(realY, false),
          width: scaleToContainer(realWidth, true),
          height: scaleToContainer(realHeight, false),
          realX,
          realY,
          realWidth,
          realHeight,
        })
      }
    }
  }
}

const addDevice = () => {
  const scope = selectedScope.value

  if (scope === 'global') {
    // 全局模式：可以添加多个设备
    if (availableDevices.value.length === 0)
      return

    const device = availableDevices.value[0]
    const globalConfig = window.appStore.get('scrcpy.global') || {}

    // 使用全局配置的默认值，如果没有则使用默认值
    const defaultWidth = Number.parseInt(globalConfig['--window-width']) || 300
    const defaultHeight = Number.parseInt(globalConfig['--window-height']) || 600
    const baseX = Number.parseInt(globalConfig['--window-x']) || 0
    const baseY = Number.parseInt(globalConfig['--window-y']) || 0

    // 为避免重叠，每个新设备偏移一些位置
    const defaultX = baseX + (arrangedDevices.value.length * 50)
    const defaultY = baseY + (arrangedDevices.value.length * 50)

    arrangedDevices.value.push({
      ...device,
      x: scaleToContainer(defaultX, true),
      y: scaleToContainer(defaultY, false),
      width: scaleToContainer(defaultWidth, true),
      height: scaleToContainer(defaultHeight, false),
      realX: defaultX,
      realY: defaultY,
      realWidth: defaultWidth,
      realHeight: defaultHeight,
    })
  }
  else {
    // 设备特定模式：只能有一个设备（当前选中的设备）
    if (arrangedDevices.value.length > 0) {
      ElMessage.warning('Device-specific mode can only arrange one device')
      return
    }

    const device = allDevices.value.find(d => d.id === scope)
    if (!device) {
      ElMessage.error('Selected device not found')
      return
    }

    const deviceConfig = window.appStore.get(`scrcpy.${scope}`) || {}
    const globalConfig = window.appStore.get('scrcpy.global') || {}

    // 设备配置优先，全局配置作为回退
    const config = { ...globalConfig, ...deviceConfig }

    const defaultWidth = Number.parseInt(config['--window-width']) || 300
    const defaultHeight = Number.parseInt(config['--window-height']) || 600
    const defaultX = Number.parseInt(config['--window-x']) || 0
    const defaultY = Number.parseInt(config['--window-y']) || 0

    arrangedDevices.value.push({
      ...device,
      x: scaleToContainer(defaultX, true),
      y: scaleToContainer(defaultY, false),
      width: scaleToContainer(defaultWidth, true),
      height: scaleToContainer(defaultHeight, false),
      realX: defaultX,
      realY: defaultY,
      realWidth: defaultWidth,
      realHeight: defaultHeight,
    })
  }
}

const removeDevice = (deviceId) => {
  const index = arrangedDevices.value.findIndex(d => d.id === deviceId)
  if (index > -1) {
    arrangedDevices.value.splice(index, 1)
  }
}

const resetLayout = () => {
  arrangedDevices.value = []
  loadLayout()
}

const deleteCurrentLayout = () => {
  if (selectedScope.value === 'global')
    return

  ElMessageBox.confirm(
    'Are you sure you want to delete this layout?',
    'Confirm',
    {
      confirmButtonText: 'Confirm',
      cancelButtonText: 'Cancel',
      type: 'warning',
    },
  ).then(() => {
    window.appStore.set(`scrcpy.${selectedScope.value}`, {})
    arrangedDevices.value = []
    ElMessage.success('Layout deleted successfully')
  }).catch(() => {})
}

// Event handlers for dragging and resizing
const onDeviceDragging = (deviceId, event) => {
  const device = arrangedDevices.value.find(d => d.id === deviceId)
  if (device) {
    device.x = event.x
    device.y = event.y
    device.realX = scaleToReal(event.x, true)
    device.realY = scaleToReal(event.y, false)
  }
}

const onDeviceResizing = (deviceId, event) => {
  const device = arrangedDevices.value.find(d => d.id === deviceId)
  if (device) {
    device.width = event.width
    device.height = event.height
    device.realWidth = scaleToReal(event.width, true)
    device.realHeight = scaleToReal(event.height, false)
  }
}

const onDeviceDragStop = (deviceId, event) => {
  onDeviceDragging(deviceId, event)
}

const onDeviceResizeStop = (deviceId, event) => {
  onDeviceResizing(deviceId, event)
}

// Dialog management
const close = () => {
  visible.value = false
}

const open = async () => {
  visible.value = true
  await nextTick()

  // Get primary display info
  try {
    const display = await window.electron?.ipcRenderer?.invoke('get-primary-display')
    if (display) {
      screenInfo.value = {
        width: display.workArea.width,
        height: display.workArea.height,
        scaleFactor: display.scaleFactor,
      }
    }
  }
  catch (error) {
    console.warn('Failed to get display info:', error)
  }

  // Update container dimensions
  await nextTick()
  if (arrangementAreaRef.value) {
    const rect = arrangementAreaRef.value.getBoundingClientRect()
    containerDimensions.value = {
      width: rect.width - 40, // Account for padding
      height: rect.height - 40,
    }
  }

  loadDevices()
  loadLayout()
}

// Scope management
const onScopeChange = (scope) => {
  selectedScope.value = scope
  loadLayout(scope)
}

// Save layout
const saveLayout = () => {
  const scope = selectedScope.value

  if (scope === 'global') {
    // 保存全局配置：使用第一个设备的配置作为全局模板
    if (arrangedDevices.value.length > 0) {
      const firstDevice = arrangedDevices.value[0]
      const globalConfig = {
        ...window.appStore.get('scrcpy.global') || {},
        '--window-width': Math.round(firstDevice.realWidth).toString(),
        '--window-height': Math.round(firstDevice.realHeight).toString(),
        '--window-x': Math.round(firstDevice.realX).toString(),
        '--window-y': Math.round(firstDevice.realY).toString(),
      }

      window.appStore.set('scrcpy.global', globalConfig)
      ElMessage.success('Global layout saved successfully')
    }
    else {
      ElMessage.warning('No devices to save')
      return
    }
  }
  else {
    // 保存设备特定配置：只保存当前设备的配置
    if (arrangedDevices.value.length > 0) {
      const device = arrangedDevices.value[0] // 设备特定模式下只有一个设备
      const deviceConfig = {
        ...window.appStore.get(`scrcpy.${scope}`) || {},
        '--window-width': Math.round(device.realWidth).toString(),
        '--window-height': Math.round(device.realHeight).toString(),
        '--window-x': Math.round(device.realX).toString(),
        '--window-y': Math.round(device.realY).toString(),
      }

      window.appStore.set(`scrcpy.${scope}`, deviceConfig)
      ElMessage.success(`Device layout saved successfully for ${device.name || device.id}`)
    }
    else {
      ElMessage.warning('No device to save')
      return
    }
  }

  // Update preference store
  try {
    if (window.usePreferenceStore) {
      const preferenceStore = window.usePreferenceStore()
      preferenceStore.init()
    }
  }
  catch (error) {
    console.warn('Failed to update preference store:', error)
  }

  close()
}

const onClosed = () => {
  arrangedDevices.value = []
  selectedScope.value = 'global'
}

// Expose methods
defineExpose({
  open,
  close,
})
</script>

<style lang="postcss" scoped>
.arrange-dialog {
  .el-dialog__body {
    padding: 20px;
    height: 70vh;
    overflow: hidden;
  }
}

.arrange-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
  gap: 10px;
}

.control-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-info {
  margin-bottom: 15px;
}

.arrangement-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.screen-container {
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.device-window {
  border: 2px solid #409eff !important;
  border-radius: 6px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.device-window:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #66b1ff !important;
}

.device-window.active {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.device-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.device-name {
  font-weight: 500;
  color: #333;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.remove-btn {
  width: 20px !important;
  height: 20px !important;
  min-height: 20px !important;
  padding: 0 !important;
  font-size: 10px;
}

.device-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-info {
  text-align: center;
  font-size: 10px;
  color: #666;
  line-height: 1.4;
}

.device-info p {
  margin: 2px 0;
}

/* Custom handle styles for better visibility */
:deep(.handle) {
  background: #409eff !important;
  border: 2px solid white !important;
  border-radius: 50%;
  width: 12px !important;
  height: 12px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.handle:hover) {
  background: #66b1ff !important;
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    align-items: stretch;
  }

  .control-section {
    justify-content: center;
  }

  .arrangement-area {
    padding: 10px;
  }
}
</style>
